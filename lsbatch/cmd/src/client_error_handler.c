/**
 * @file client_error_handler.c
 * @brief 客户端错误处理器实现 - Day 25: 错误处理器
 */

#include "client_error_handler.h"
#include "connection_manager_simple.h"
#include "batch_data_transmitter.h"
#include "response_processor.h"

/* ==================== 私有函数声明 ==================== */

static void log_error_to_file(client_error_handler_t *handler,
                             client_error_type_t error_type,
                             const char *error_message);

static void update_error_statistics(client_error_handler_t *handler,
                                   client_error_type_t error_type);

static int calculate_retry_delay(client_error_handler_t *handler);

static error_level_t get_error_level(client_error_type_t error_type);

/* ==================== 公共接口函数实现 ==================== */

/**
 * @brief 创建默认错误处理器配置
 */
void client_error_handler_create_default_config(client_error_handler_config_t *config)
{
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(client_error_handler_config_t));
    
    /* 重试配置 */
    config->max_retries = 3;
    config->retry_delay_ms = 1000;
    config->exponential_backoff = true;
    config->max_retry_delay_ms = 30000;
    
    /* 错误记录配置 */
    config->enable_error_logging = true;
    snprintf(config->error_log_file, sizeof(config->error_log_file), 
             "/tmp/lsf_client_errors.log");
    config->max_error_log_size_mb = 10;
    config->rotate_error_logs = true;
    
    /* 错误处理策略 */
    config->fail_fast_on_critical = true;
    config->continue_on_recoverable = true;
    config->error_threshold_per_minute = 100;
    
    /* 通知配置 */
    config->enable_error_callbacks = true;
    config->enable_error_statistics = true;
}

/**
 * @brief 创建错误处理器
 */
int client_error_handler_create(client_error_handler_t *handler, 
                               const client_error_handler_config_t *config)
{
    if (!handler || !config) {
        return -1;
    }
    
    memset(handler, 0, sizeof(client_error_handler_t));
    
    /* 复制配置 */
    handler->config = *config;
    
    /* 初始化统计信息 */
    memset(&handler->statistics, 0, sizeof(error_statistics_t));
    handler->statistics.first_error_time = 0;
    handler->statistics.last_error_time = 0;
    
    /* 初始化重试状态 */
    memset(&handler->retry_state, 0, sizeof(retry_state_t));
    handler->retry_state.current_delay_ms = config->retry_delay_ms;
    
    /* 打开错误日志文件 */
    if (config->enable_error_logging) {
        handler->error_log = fopen(config->error_log_file, "a");
        if (!handler->error_log) {
            fprintf(stderr, "Warning: Cannot open error log file: %s\n", 
                    config->error_log_file);
        }
    }
    
    handler->is_initialized = true;
    handler->is_enabled = true;
    
    fprintf(stderr, "✅ 客户端错误处理器初始化成功\n");
    return 0;
}

/**
 * @brief 设置关联模块
 */
void client_error_handler_set_modules(client_error_handler_t *handler,
                                     connection_manager_t *conn_mgr,
                                     batch_data_transmitter_t *transmitter,
                                     response_processor_t *processor)
{
    if (!handler) {
        return;
    }
    
    handler->connection_manager = conn_mgr;
    handler->data_transmitter = transmitter;
    handler->response_processor = processor;
    
    fprintf(stderr, "🔗 错误处理器模块关联设置完成\n");
}

/**
 * @brief 处理错误
 */
int client_error_handler_handle_error(client_error_handler_t *handler,
                                     client_error_type_t error_type,
                                     const char *error_message,
                                     int job_index)
{
    if (!handler || !handler->is_enabled) {
        return -1;
    }
    
    /* 记录错误到日志 */
    if (handler->config.enable_error_logging) {
        log_error_to_file(handler, error_type, error_message);
    }
    
    /* 更新统计信息 */
    if (handler->config.enable_error_statistics) {
        update_error_statistics(handler, error_type);
    }
    
    /* 调用错误回调 */
    if (handler->config.enable_error_callbacks && handler->error_callback) {
        handler->error_callback(error_type, error_message, handler->callback_user_data);
    }
    
    /* 判断错误级别 */
    error_level_t level = get_error_level(error_type);
    
    /* 关键错误快速失败 */
    if (level == ERROR_LEVEL_CRITICAL && handler->config.fail_fast_on_critical) {
        fprintf(stderr, "❌ 关键错误，快速失败: %s\n", error_message);
        return -1;
    }
    
    fprintf(stderr, "⚠️  错误处理完成: [%s] %s (作业索引: %d)\n", 
            client_error_type_to_string(error_type), error_message, job_index);
    
    return 0;
}

/**
 * @brief 判断是否应该重试
 */
bool client_error_handler_should_retry(client_error_handler_t *handler,
                                      client_error_type_t error_type)
{
    if (!handler || !handler->is_enabled) {
        return false;
    }
    
    /* 检查重试次数限制 */
    if (handler->retry_state.current_retry_count >= handler->config.max_retries) {
        return false;
    }
    
    /* 根据错误类型判断是否可重试 */
    switch (error_type) {
        case CLIENT_ERROR_NETWORK:
        case CLIENT_ERROR_TIMEOUT:
        case CLIENT_ERROR_SERVER:
            return true;
            
        case CLIENT_ERROR_AUTH:
        case CLIENT_ERROR_PARSE:
        case CLIENT_ERROR_CONFIG:
            return false;
            
        default:
            return handler->config.continue_on_recoverable;
    }
}

/**
 * @brief 执行重试操作
 */
int client_error_handler_retry_operation(client_error_handler_t *handler,
                                        int (*operation_func)(void *),
                                        void *operation_data)
{
    if (!handler || !operation_func) {
        return -1;
    }
    
    int delay_ms = calculate_retry_delay(handler);
    
    fprintf(stderr, "🔄 准备重试操作，延迟 %d 毫秒...\n", delay_ms);
    
    /* 等待重试延迟 */
    usleep(delay_ms * 1000);
    
    /* 更新重试状态 */
    handler->retry_state.current_retry_count++;
    handler->retry_state.last_retry_time = time(NULL);
    handler->retry_state.is_retrying = true;
    
    /* 执行重试操作 */
    int result = operation_func(operation_data);
    
    handler->retry_state.is_retrying = false;
    
    if (result == 0) {
        handler->statistics.successful_retries++;
        fprintf(stderr, "✅ 重试操作成功\n");
    } else {
        handler->statistics.failed_retries++;
        fprintf(stderr, "❌ 重试操作失败\n");
    }
    
    handler->statistics.total_retries++;
    
    return result;
}

/**
 * @brief 获取错误统计信息
 */
void client_error_handler_get_statistics(const client_error_handler_t *handler,
                                        error_statistics_t *stats)
{
    if (!handler || !stats) {
        return;
    }
    
    *stats = handler->statistics;
}

/**
 * @brief 重置错误统计
 */
void client_error_handler_reset_statistics(client_error_handler_t *handler)
{
    if (!handler) {
        return;
    }
    
    memset(&handler->statistics, 0, sizeof(error_statistics_t));
    memset(&handler->retry_state, 0, sizeof(retry_state_t));
    handler->retry_state.current_delay_ms = handler->config.retry_delay_ms;
    
    fprintf(stderr, "🔄 错误统计信息已重置\n");
}

/**
 * @brief 生成错误报告
 */
int client_error_handler_generate_report(const client_error_handler_t *handler,
                                        const char *report_file)
{
    if (!handler || !report_file) {
        return -1;
    }
    
    FILE *fp = fopen(report_file, "w");
    if (!fp) {
        return -1;
    }
    
    fprintf(fp, "=== LSF 客户端错误处理报告 ===\n");
    fprintf(fp, "生成时间: %s", ctime(&(time_t){time(NULL)}));
    fprintf(fp, "\n错误统计:\n");
    fprintf(fp, "  总错误数: %d\n", handler->statistics.total_errors);
    fprintf(fp, "  总重试次数: %d\n", handler->statistics.total_retries);
    fprintf(fp, "  成功重试: %d\n", handler->statistics.successful_retries);
    fprintf(fp, "  失败重试: %d\n", handler->statistics.failed_retries);
    
    if (handler->statistics.first_error_time > 0) {
        fprintf(fp, "  首次错误时间: %s", ctime(&handler->statistics.first_error_time));
    }
    if (handler->statistics.last_error_time > 0) {
        fprintf(fp, "  最后错误时间: %s", ctime(&handler->statistics.last_error_time));
    }
    
    fclose(fp);
    
    fprintf(stderr, "📊 错误报告已生成: %s\n", report_file);
    return 0;
}

/**
 * @brief 设置错误回调函数
 */
void client_error_handler_set_callback(client_error_handler_t *handler,
                                      void (*callback)(client_error_type_t, const char *, void *),
                                      void *user_data)
{
    if (!handler) {
        return;
    }
    
    handler->error_callback = callback;
    handler->callback_user_data = user_data;
}

/**
 * @brief 清理错误处理器
 */
void client_error_handler_cleanup(client_error_handler_t *handler)
{
    if (!handler) {
        return;
    }
    
    if (handler->error_log) {
        fclose(handler->error_log);
        handler->error_log = NULL;
    }
    
    handler->is_initialized = false;
    handler->is_enabled = false;
    
    fprintf(stderr, "🧹 客户端错误处理器清理完成\n");
}

/**
 * @brief 错误类型转字符串
 */
const char* client_error_type_to_string(client_error_type_t error_type)
{
    switch (error_type) {
        case CLIENT_ERROR_NONE: return "无错误";
        case CLIENT_ERROR_PARSE: return "解析错误";
        case CLIENT_ERROR_MEMORY: return "内存错误";
        case CLIENT_ERROR_NETWORK: return "网络错误";
        case CLIENT_ERROR_PROTOCOL: return "协议错误";
        case CLIENT_ERROR_AUTH: return "认证错误";
        case CLIENT_ERROR_TIMEOUT: return "超时错误";
        case CLIENT_ERROR_SERVER: return "服务器错误";
        case CLIENT_ERROR_CONFIG: return "配置错误";
        case CLIENT_ERROR_RESOURCE: return "资源错误";
        default: return "未知错误";
    }
}

/**
 * @brief 错误级别转字符串
 */
const char* error_level_to_string(error_level_t level)
{
    switch (level) {
        case ERROR_LEVEL_INFO: return "信息";
        case ERROR_LEVEL_WARNING: return "警告";
        case ERROR_LEVEL_ERROR: return "错误";
        case ERROR_LEVEL_CRITICAL: return "关键";
        default: return "未知";
    }
}

/* ==================== 私有函数实现 ==================== */

/**
 * @brief 记录错误到日志文件
 */
static void log_error_to_file(client_error_handler_t *handler,
                             client_error_type_t error_type,
                             const char *error_message)
{
    if (!handler || !handler->error_log || !error_message) {
        return;
    }

    time_t now = time(NULL);
    char time_str[64];
    strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", localtime(&now));

    fprintf(handler->error_log, "[%s] [%s] %s: %s\n",
            time_str,
            error_level_to_string(get_error_level(error_type)),
            client_error_type_to_string(error_type),
            error_message);

    fflush(handler->error_log);
}

/**
 * @brief 更新错误统计信息
 */
static void update_error_statistics(client_error_handler_t *handler,
                                   client_error_type_t error_type)
{
    if (!handler) {
        return;
    }

    handler->statistics.total_errors++;

    /* 按类型统计 */
    if (error_type >= CLIENT_ERROR_PARSE && error_type <= CLIENT_ERROR_RESOURCE) {
        int index = (error_type - CLIENT_ERROR_PARSE) % 10;
        handler->statistics.errors_by_type[index]++;
    }

    /* 按级别统计 */
    error_level_t level = get_error_level(error_type);
    if (level >= ERROR_LEVEL_INFO && level <= ERROR_LEVEL_CRITICAL) {
        handler->statistics.errors_by_level[level]++;
    }

    /* 更新时间戳 */
    time_t now = time(NULL);
    if (handler->statistics.first_error_time == 0) {
        handler->statistics.first_error_time = now;
    }
    handler->statistics.last_error_time = now;
}

/**
 * @brief 计算重试延迟时间
 */
static int calculate_retry_delay(client_error_handler_t *handler)
{
    if (!handler) {
        return 1000;
    }

    int delay = handler->retry_state.current_delay_ms;

    if (handler->config.exponential_backoff) {
        /* 指数退避算法 */
        delay = handler->config.retry_delay_ms *
                (1 << handler->retry_state.current_retry_count);

        /* 限制最大延迟 */
        if (delay > handler->config.max_retry_delay_ms) {
            delay = handler->config.max_retry_delay_ms;
        }
    }

    handler->retry_state.current_delay_ms = delay;
    return delay;
}

/**
 * @brief 获取错误级别
 */
static error_level_t get_error_level(client_error_type_t error_type)
{
    switch (error_type) {
        case CLIENT_ERROR_NONE:
            return ERROR_LEVEL_INFO;

        case CLIENT_ERROR_TIMEOUT:
        case CLIENT_ERROR_NETWORK:
            return ERROR_LEVEL_WARNING;

        case CLIENT_ERROR_PARSE:
        case CLIENT_ERROR_CONFIG:
        case CLIENT_ERROR_PROTOCOL:
        case CLIENT_ERROR_SERVER:
            return ERROR_LEVEL_ERROR;

        case CLIENT_ERROR_MEMORY:
        case CLIENT_ERROR_AUTH:
        case CLIENT_ERROR_RESOURCE:
            return ERROR_LEVEL_CRITICAL;

        default:
            return ERROR_LEVEL_ERROR;
    }
}
