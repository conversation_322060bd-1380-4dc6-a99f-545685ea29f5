/**
 * @file client_error_handler.h
 * @brief 客户端错误处理器定义 - Day 25: 错误处理器
 */

#ifndef CLIENT_ERROR_HANDLER_H
#define CLIENT_ERROR_HANDLER_H

#include <stdio.h>
#include <stdbool.h>
#include <time.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>
#include <sys/time.h>
#include <unistd.h>
#include <netinet/tcp.h>
#include <sys/socket.h>
#include <stdint.h>

/* 前向声明 */
typedef struct connection_manager connection_manager_t;
typedef struct batch_data_transmitter batch_data_transmitter_t;
typedef struct response_processor response_processor_t;

/* ==================== 错误类型定义 ==================== */

/**
 * @brief 错误类型枚举
 */
typedef enum {
    CLIENT_ERROR_NONE = 0,
    CLIENT_ERROR_PARSE = 1000,      /* 解析错误 */
    CLIENT_ERROR_MEMORY = 1001,     /* 内存错误 */
    CLIENT_ERROR_NETWORK = 1002,    /* 网络错误 */
    CLIENT_ERROR_PROTOCOL = 1003,   /* 协议错误 */
    CLIENT_ERROR_AUTH = 1004,       /* 认证错误 */
    CLIENT_ERROR_TIMEOUT = 1005,    /* 超时错误 */
    CLIENT_ERROR_SERVER = 1006,     /* 服务器错误 */
    CLIENT_ERROR_CONFIG = 1007,     /* 配置错误 */
    CLIENT_ERROR_RESOURCE = 1008,   /* 资源错误 */
    CLIENT_ERROR_UNKNOWN = 9999     /* 未知错误 */
} client_error_type_t;

/**
 * @brief 错误严重级别
 */
typedef enum {
    ERROR_LEVEL_INFO = 0,
    ERROR_LEVEL_WARNING = 1,
    ERROR_LEVEL_ERROR = 2,
    ERROR_LEVEL_CRITICAL = 3
} error_level_t;

/* ==================== 错误处理配置 ==================== */

/**
 * @brief 错误处理配置
 */
typedef struct {
    /* 重试配置 */
    int max_retries;                    /* 最大重试次数 */
    int retry_delay_ms;                 /* 重试延迟(毫秒) */
    bool exponential_backoff;           /* 是否使用指数退避 */
    int max_retry_delay_ms;             /* 最大重试延迟 */
    
    /* 错误记录配置 */
    bool enable_error_logging;          /* 是否启用错误日志 */
    char error_log_file[256];           /* 错误日志文件路径 */
    int max_error_log_size_mb;          /* 最大日志文件大小(MB) */
    bool rotate_error_logs;             /* 是否轮转日志 */
    
    /* 错误处理策略 */
    bool fail_fast_on_critical;        /* 关键错误时快速失败 */
    bool continue_on_recoverable;       /* 可恢复错误时继续 */
    int error_threshold_per_minute;     /* 每分钟错误阈值 */
    
    /* 通知配置 */
    bool enable_error_callbacks;       /* 是否启用错误回调 */
    bool enable_error_statistics;      /* 是否启用错误统计 */
} client_error_handler_config_t;

/* ==================== 错误统计信息 ==================== */

/**
 * @brief 错误统计信息
 */
typedef struct {
    /* 错误计数 */
    int total_errors;                   /* 总错误数 */
    int errors_by_type[10];             /* 按类型分类的错误数 */
    int errors_by_level[4];             /* 按级别分类的错误数 */
    
    /* 重试统计 */
    int total_retries;                  /* 总重试次数 */
    int successful_retries;             /* 成功重试次数 */
    int failed_retries;                 /* 失败重试次数 */
    
    /* 时间统计 */
    time_t first_error_time;            /* 第一个错误时间 */
    time_t last_error_time;             /* 最后一个错误时间 */
    double average_recovery_time_ms;    /* 平均恢复时间 */
    
    /* 性能影响 */
    int operations_affected;            /* 受影响的操作数 */
    double total_delay_ms;              /* 总延迟时间 */
} error_statistics_t;

/* ==================== 重试状态 ==================== */

/**
 * @brief 重试状态信息
 */
typedef struct {
    int current_retry_count;            /* 当前重试次数 */
    time_t last_retry_time;             /* 上次重试时间 */
    int current_delay_ms;               /* 当前延迟时间 */
    client_error_type_t last_error_type; /* 上次错误类型 */
    bool is_retrying;                   /* 是否正在重试 */
} retry_state_t;

/* ==================== 错误处理器结构 ==================== */

/**
 * @brief 客户端错误处理器
 */
typedef struct client_error_handler {
    /* 配置信息 */
    client_error_handler_config_t config;
    
    /* 统计信息 */
    error_statistics_t statistics;
    
    /* 重试状态 */
    retry_state_t retry_state;
    
    /* 日志文件 */
    FILE *error_log;
    
    /* 模块引用 */
    connection_manager_t *connection_manager;
    batch_data_transmitter_t *data_transmitter;
    response_processor_t *response_processor;
    
    /* 状态标志 */
    bool is_initialized;
    bool is_enabled;
    
    /* 错误回调函数 */
    void (*error_callback)(client_error_type_t error_type, 
                          const char *error_message, 
                          void *user_data);
    void *callback_user_data;
} client_error_handler_t;

/* ==================== 公共接口函数声明 ==================== */

/**
 * @brief 创建默认错误处理器配置
 */
void client_error_handler_create_default_config(client_error_handler_config_t *config);

/**
 * @brief 创建错误处理器
 */
int client_error_handler_create(client_error_handler_t *handler, 
                               const client_error_handler_config_t *config);

/**
 * @brief 设置关联模块
 */
void client_error_handler_set_modules(client_error_handler_t *handler,
                                     connection_manager_t *conn_mgr,
                                     batch_data_transmitter_t *transmitter,
                                     response_processor_t *processor);

/**
 * @brief 处理错误
 */
int client_error_handler_handle_error(client_error_handler_t *handler,
                                     client_error_type_t error_type,
                                     const char *error_message,
                                     int job_index);

/**
 * @brief 判断是否应该重试
 */
bool client_error_handler_should_retry(client_error_handler_t *handler,
                                      client_error_type_t error_type);

/**
 * @brief 执行重试操作
 */
int client_error_handler_retry_operation(client_error_handler_t *handler,
                                        int (*operation_func)(void *),
                                        void *operation_data);

/**
 * @brief 获取错误统计信息
 */
void client_error_handler_get_statistics(const client_error_handler_t *handler,
                                        error_statistics_t *stats);

/**
 * @brief 重置错误统计
 */
void client_error_handler_reset_statistics(client_error_handler_t *handler);

/**
 * @brief 生成错误报告
 */
int client_error_handler_generate_report(const client_error_handler_t *handler,
                                        const char *report_file);

/**
 * @brief 设置错误回调函数
 */
void client_error_handler_set_callback(client_error_handler_t *handler,
                                      void (*callback)(client_error_type_t, const char *, void *),
                                      void *user_data);

/**
 * @brief 清理错误处理器
 */
void client_error_handler_cleanup(client_error_handler_t *handler);

/**
 * @brief 错误类型转字符串
 */
const char* client_error_type_to_string(client_error_type_t error_type);

/**
 * @brief 错误级别转字符串
 */
const char* error_level_to_string(error_level_t level);

#endif /* CLIENT_ERROR_HANDLER_H */
